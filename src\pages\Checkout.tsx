import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { paymentService } from "@/lib/paymentService";
import { ArrowLeftCircle, Coins, ListChecks } from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";

const Checkout = () => {
  // get selected slots from state
  const { selectedSlots } = useLocation().state as { selectedSlots: any };

  // navigate back to slots page
  const navigate = useNavigate();
  const handleBackToSlots = () => {
    navigate("/slots");
  };

  //   calculate total time
  const totalMinutes = selectedSlots.reduce((acc: any) => acc + 30, 0);
  const totalHours = Math.floor(totalMinutes / 60);
  let totalTime = "";
  if (totalMinutes % 60 !== 0) {
    const remainingMinutes = totalMinutes % 60;
    totalTime = `${totalHours} hours and ${remainingMinutes} minutes`;
  } else {
    totalTime = `${totalHours} hours`;
  }

  return (
    <>
      <section>
        <div className="p-5 md:relative">
          <Button
            className="mt-5 p-4 md:p-5 md:absolute text-lg md:text-xl flex items-center cursor-pointer"
            onClick={handleBackToSlots}
          >
            <ArrowLeftCircle size={30} /> Back{" "}
            <span className="md:block hidden">to Slots</span>
          </Button>
          <h1 className="scroll-m-20 text-center text-[2rem] font-extrabold tracking-tight text-balance leading-[55px] text-gray-800">
            Checkout
          </h1>
        </div>
      </section>

      <section className="mt-10">
        <div>
          <div className=" w-[90%] md:w-[80%] mx-auto p-5 md:p-10 rounded-lg">
            <h1 className="scroll-m-20 text-center text-[2rem] md:text-[3rem] font-semibold tracking-tight text-balance text-gray-800">
              Complete your Booking
            </h1>
            <p className="text-gray-700 text-center text-[1.2rem] md:text-[1.3rem] mt-5">
              Enter your details to complete your field reservation
            </p>

            {/* booking summary */}
            <div className="mt-10 bg-white p-5 md:p-10 rounded-lg">
              <h3 className="scroll-m-20 text-[1.3rem] font-bold tracking-tight text-balance  flex items-center gap-2 text-gray-800">
                <ListChecks color="green" /> Booking Summary
              </h3>
              <div className="mt-5">
                {selectedSlots.map((slot: any) => (
                  <div
                    key={slot.id}
                    className="flex flex-col md:flex-row md:items-center md:justify-between border-b-[1px] border-gray-300 py-3 gap-2"
                  >
                    <p className="text-sm md:text-base">
                      {slot.startTime} - {slot.endTime} on {slot.date}
                    </p>
                    <p className="text-sm md:text-base">
                      <span className="font-bold">Price: </span>
                      {new Intl.NumberFormat("en-TZ", {
                        style: "currency",
                        currency: "TZS",
                      }).format(slot.price)}
                    </p>
                  </div>
                ))}
                <div>
                  <p className="mt-5 text-gray-700 text-[1rem] md:text-[1.3rem] text-center font-extrabold">
                    Total Time: {totalTime}
                  </p>
                </div>
              </div>
              <div className="mt-10">
                <h3 className="scroll-m-20 text-[1.3rem] font-bold tracking-tight text-balance  flex items-center gap-2 text-gray-800">
                  <Coins color="green" /> Total Amount
                </h3>
                <h1 className="scroll-m-20 text-center text-[2.3rem] md:text-[4rem] font-extrabold tracking-tight text-balance leading-[45px] md:leading-[55px] text-green-600 mt-5">
                  {new Intl.NumberFormat("en-TZ", {
                    style: "currency",
                    currency: "TZS",
                  }).format(
                    selectedSlots.reduce(
                      (acc: any, slot: any) => acc + slot.price,
                      0
                    ) || 0
                  )}
                </h1>
              </div>
            </div>

            {/* Personal details */}
            <div className="mt-10 bg-white p-5 md:p-10 rounded-lg">
              <h3 className="scroll-m-20 text-[1.3rem] font-bold tracking-tight text-balance  flex items-center gap-2 text-gray-800">
                Personal Details
              </h3>
              <div className="mt-5">
                <label className="text-gray-700 text-lg font-semibold">
                  Name: *
                </label>
                <Input
                  type="text"
                  className="w-full border-[1px] border-gray-300 p-2 rounded-lg mt-2"
                  required
                />
              </div>
              <div className="mt-5">
                <label className="text-gray-700 text-lg font-semibold">
                  Email: *
                </label>
                <Input
                  type="email"
                  className="w-full border-[1px] border-gray-300 p-2 rounded-lg mt-2"
                  required
                />
              </div>
              <div className="mt-5">
                <label className="text-gray-700 text-lg font-semibold">
                  Phone Number: *
                </label>
                <Input
                  type="text"
                  className="w-full border-[1px] border-gray-300 p-2 rounded-lg mt-2"
                  required
                />
              </div>
            </div>
            <div className="mt-10">
              <Button
                className="mt-5 p-4 md:p-7 text-lg md:text-xl flex items-center cursor-pointer"
                onClick={() => navigate("/confirmation")}
              >
                Confirm Booking
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* azampay functionality testing */}
      <section className="mt-10">
        <div>
          <div className=" w-[90%] md:w-[80%] mx-auto p-5 md:p-10 rounded-lg">
            <h1 className="scroll-m-20 text-center text-[2rem] font-extrabold tracking-tight text-balance leading-[55px] text-gray-800">
              Azampay Testing
            </h1>
            <div className="mt-10">
              <Button
                className="mt-5 p-4 md:p-7 text-lg md:text-xl flex items-center cursor-pointer"
                onClick={() => paymentService.generateToken()}
              >
                Test Azampay
              </Button>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Checkout;
