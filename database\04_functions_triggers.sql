-- FieldEase Database Schema - Functions and Triggers
-- Execute this file after creating tables, indexes, and RLS policies

-- Function to automatically update slot availability when booking is created
CREATE OR REPLACE FUNCTION update_slot_availability()
RETURNS TRIGGER AS $$
BEGIN
    -- When a booking is created, mark associated slots as unavailable
    IF TG_OP = 'INSERT' THEN
        UPDATE slots 
        SET is_available = FALSE, booking_id = NEW.id
        WHERE id = ANY(
            SELECT unnest(string_to_array(NEW.slot_ids, ','))::UUID
        ) AND is_available = TRUE;
        RETURN NEW;
    END IF;
    
    -- When a booking is deleted, mark associated slots as available
    IF TG_OP = 'DELETE' THEN
        UPDATE slots 
        SET is_available = TRUE, booking_id = NULL
        WHERE booking_id = OLD.id;
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to prevent double booking
CREATE OR REPLACE FUNCTION prevent_double_booking()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if slot is already booked
    IF NEW.is_available = FALSE AND OLD.is_available = TRUE THEN
        IF EXISTS (
            SELECT 1 FROM slots 
            WHERE id = NEW.id 
            AND is_available = FALSE 
            AND booking_id IS NOT NULL
        ) THEN
            RAISE EXCEPTION 'Slot is already booked';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate slot time constraints
CREATE OR REPLACE FUNCTION validate_slot_times()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure start_time is before end_time
    IF NEW.start_time >= NEW.end_time THEN
        RAISE EXCEPTION 'Start time must be before end time';
    END IF;
    
    -- Ensure slot duration is reasonable (between 15 minutes and 4 hours)
    IF (NEW.end_time - NEW.start_time) < INTERVAL '15 minutes' THEN
        RAISE EXCEPTION 'Slot duration must be at least 15 minutes';
    END IF;
    
    IF (NEW.end_time - NEW.start_time) > INTERVAL '4 hours' THEN
        RAISE EXCEPTION 'Slot duration cannot exceed 4 hours';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate booking reference ID
CREATE OR REPLACE FUNCTION generate_booking_reference()
RETURNS TRIGGER AS $$
BEGIN
    -- Add a reference field to bookings table if it doesn't exist
    -- This would need to be added to the table schema
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER prevent_double_booking_trigger
    BEFORE UPDATE ON slots
    FOR EACH ROW
    EXECUTE FUNCTION prevent_double_booking();

CREATE TRIGGER validate_slot_times_trigger
    BEFORE INSERT OR UPDATE ON slots
    FOR EACH ROW
    EXECUTE FUNCTION validate_slot_times();

-- Function to get available slots for a date
CREATE OR REPLACE FUNCTION get_available_slots(slot_date DATE)
RETURNS TABLE (
    id UUID,
    date DATE,
    start_time TIME,
    end_time TIME,
    price INTEGER,
    is_available BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT s.id, s.date, s.start_time, s.end_time, s.price, s.is_available
    FROM slots s
    WHERE s.date = slot_date 
    AND s.is_available = TRUE
    ORDER BY s.start_time;
END;
$$ LANGUAGE plpgsql;

-- Function to book multiple slots
CREATE OR REPLACE FUNCTION book_slots(
    customer_name TEXT,
    customer_phone TEXT,
    customer_email TEXT,
    booking_date DATE,
    slot_ids UUID[]
)
RETURNS UUID AS $$
DECLARE
    booking_id UUID;
    slot_id UUID;
BEGIN
    -- Create the booking
    INSERT INTO bookings (name, phone, email, date)
    VALUES (customer_name, customer_phone, customer_email, booking_date)
    RETURNING id INTO booking_id;
    
    -- Update each slot
    FOREACH slot_id IN ARRAY slot_ids
    LOOP
        UPDATE slots 
        SET is_available = FALSE, booking_id = booking_id
        WHERE id = slot_id AND is_available = TRUE;
        
        -- Check if the update was successful
        IF NOT FOUND THEN
            RAISE EXCEPTION 'Slot % is not available for booking', slot_id;
        END IF;
    END LOOP;
    
    RETURN booking_id;
END;
$$ LANGUAGE plpgsql;
