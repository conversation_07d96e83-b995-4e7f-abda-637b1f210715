// FieldEase Slot Generation Script for Supabase
// Run this script to generate slots in your Supabase database
// Usage: npx tsx scripts/generateSlots.ts

import { createClient } from '@supabase/supabase-js';

// Configure your Supabase connection
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'your-supabase-url';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-anon-key';

const supabase = createClient(supabaseUrl, supabaseKey);

interface SlotData {
  date: string;
  start_time: string;
  end_time: string;
  price: number;
  is_available: boolean;
}

// Generate slots exactly like frontend logic
function generateSlotsData(startDate: Date, endDate: Date): SlotData[] {
  const slots: SlotData[] = [];
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    // Skip Sundays (optional - remove this if you want to include Sundays)
    if (currentDate.getDay() === 0) {
      currentDate.setDate(currentDate.getDate() + 1);
      continue;
    }

    const dateStr = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD format

    // Generate slots from 7:00 AM to 8:30 PM (matches your frontend)
    for (let hour = 7; hour <= 20; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        if (hour === 20 && minute === 30) break; // Stop at 8:30 PM

        const startHour = hour.toString().padStart(2, '0');
        const startMinute = minute.toString().padStart(2, '0');
        const endHour = minute === 30 ? (hour + 1).toString().padStart(2, '0') : startHour;
        const endMinute = minute === 30 ? '00' : '30';

        slots.push({
          date: dateStr,
          start_time: `${startHour}:${startMinute}:00`,
          end_time: `${endHour}:${endMinute}:00`,
          price: 15000, // Price in cents (150.00 TZS)
          is_available: true,
        });
      }
    }

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return slots;
}

// Batch insert slots to Supabase
async function insertSlotsToSupabase(slots: SlotData[], batchSize: number = 100) {
  console.log(`Inserting ${slots.length} slots in batches of ${batchSize}...`);
  
  let inserted = 0;
  let errors = 0;

  for (let i = 0; i < slots.length; i += batchSize) {
    const batch = slots.slice(i, i + batchSize);
    
    try {
      const { data, error } = await supabase
        .from('slots')
        .insert(batch)
        .select('id');

      if (error) {
        console.error(`Batch ${Math.floor(i / batchSize) + 1} error:`, error);
        errors += batch.length;
      } else {
        inserted += data?.length || 0;
        console.log(`✅ Batch ${Math.floor(i / batchSize) + 1}: ${data?.length} slots inserted`);
      }
    } catch (err) {
      console.error(`Batch ${Math.floor(i / batchSize) + 1} failed:`, err);
      errors += batch.length;
    }
  }

  return { inserted, errors };
}

// Clear existing slots (optional)
async function clearExistingSlots(startDate: string, endDate: string) {
  console.log(`Clearing existing unbooked slots from ${startDate} to ${endDate}...`);
  
  const { data, error } = await supabase
    .from('slots')
    .delete()
    .gte('date', startDate)
    .lte('date', endDate)
    .is('booking_id', null); // Only delete unbooked slots

  if (error) {
    console.error('Error clearing slots:', error);
    return 0;
  }

  console.log(`🗑️ Cleared ${data?.length || 0} existing slots`);
  return data?.length || 0;
}

// Main execution function
async function generateSlots(options: {
  daysAhead?: number;
  clearExisting?: boolean;
  startDate?: Date;
  endDate?: Date;
}) {
  const {
    daysAhead = 90,
    clearExisting = false,
    startDate = new Date(),
    endDate = new Date(Date.now() + daysAhead * 24 * 60 * 60 * 1000)
  } = options;

  console.log('🚀 FieldEase Slot Generation Started');
  console.log(`📅 Date range: ${startDate.toDateString()} to ${endDate.toDateString()}`);

  try {
    // Clear existing slots if requested
    if (clearExisting) {
      await clearExistingSlots(
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      );
    }

    // Generate slot data
    console.log('⚙️ Generating slot data...');
    const slots = generateSlotsData(startDate, endDate);
    console.log(`📊 Generated ${slots.length} slots`);

    // Insert to Supabase
    const { inserted, errors } = await insertSlotsToSupabase(slots);

    // Summary
    console.log('\n📈 Generation Summary:');
    console.log(`✅ Successfully inserted: ${inserted} slots`);
    console.log(`❌ Errors: ${errors} slots`);
    console.log(`📅 Date range: ${startDate.toDateString()} to ${endDate.toDateString()}`);
    console.log('🎉 Slot generation completed!');

  } catch (error) {
    console.error('❌ Slot generation failed:', error);
  }
}

// Script execution
if (require.main === module) {
  // Parse command line arguments
  const args = process.argv.slice(2);
  const daysAhead = args[0] ? parseInt(args[0]) : 90;
  const clearExisting = args.includes('--clear');

  generateSlots({
    daysAhead,
    clearExisting
  });
}

export { generateSlots, generateSlotsData, insertSlotsToSupabase };
