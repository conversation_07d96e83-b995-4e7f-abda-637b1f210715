-- FieldEase Database Schema - Sample Data
-- Execute this file last to populate tables with initial data

-- Insert sample admin users
INSERT INTO admin (name, email) VALUES
('<PERSON> Admin', '<EMAIL>'),
('Sarah Manager', '<EMAIL>')
ON CONFLICT (email) DO NOTHING;

-- Function to generate time slots for multiple days
CREATE OR REPLACE FUNCTION generate_sample_slots()
RETURNS VOID AS $$
DECLARE
    current_date DATE := CURRENT_DATE;
    end_date DATE := CURRENT_DATE + INTERVAL '60 days';
    slot_start TIME;
    slot_end TIME;
    hour_counter INTEGER;
BEGIN
    -- Loop through each date
    WHILE current_date <= end_date LOOP
        -- Skip Sundays (assuming field is closed on Sundays)
        IF EXTRACT(DOW FROM current_date) != 0 THEN
            -- Generate slots from 8:00 AM to 8:00 PM (12 hours)
            FOR hour_counter IN 8..19 LOOP
                -- 30-minute slots
                slot_start := (hour_counter || ':00:00')::TIME;
                slot_end := (hour_counter || ':30:00')::TIME;
                
                INSERT INTO slots (date, start_time, end_time, price, is_available)
                VALUES (current_date, slot_start, slot_end, 15000, TRUE);
                
                slot_start := (hour_counter || ':30:00')::TIME;
                slot_end := ((hour_counter + 1) || ':00:00')::TIME;
                
                INSERT INTO slots (date, start_time, end_time, price, is_available)
                VALUES (current_date, slot_start, slot_end, 15000, TRUE);
            END LOOP;
        END IF;
        
        current_date := current_date + INTERVAL '1 day';
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the function to generate sample slots
SELECT generate_sample_slots();

-- Insert some sample bookings for demonstration
INSERT INTO bookings (name, phone, email, date) VALUES
('Alice Johnson', '+************', '<EMAIL>', CURRENT_DATE + 1),
('Bob Smith', '+************', '<EMAIL>', CURRENT_DATE + 2),
('Carol Davis', '+************', '<EMAIL>', CURRENT_DATE + 3)
ON CONFLICT DO NOTHING;

-- Book some sample slots (mark them as unavailable)
DO $$
DECLARE
    alice_booking_id UUID;
    bob_booking_id UUID;
    carol_booking_id UUID;
    sample_slot_id UUID;
BEGIN
    -- Get booking IDs
    SELECT id INTO alice_booking_id FROM bookings WHERE email = '<EMAIL>';
    SELECT id INTO bob_booking_id FROM bookings WHERE email = '<EMAIL>';
    SELECT id INTO carol_booking_id FROM bookings WHERE email = '<EMAIL>';
    
    -- Book some slots for Alice (tomorrow, 10:00-11:00)
    SELECT id INTO sample_slot_id FROM slots 
    WHERE date = CURRENT_DATE + 1 AND start_time = '10:00:00' 
    LIMIT 1;
    
    IF sample_slot_id IS NOT NULL THEN
        UPDATE slots 
        SET is_available = FALSE, booking_id = alice_booking_id
        WHERE id = sample_slot_id;
    END IF;
    
    SELECT id INTO sample_slot_id FROM slots 
    WHERE date = CURRENT_DATE + 1 AND start_time = '10:30:00' 
    LIMIT 1;
    
    IF sample_slot_id IS NOT NULL THEN
        UPDATE slots 
        SET is_available = FALSE, booking_id = alice_booking_id
        WHERE id = sample_slot_id;
    END IF;
    
    -- Book some slots for Bob (day after tomorrow, 14:00-15:00)
    SELECT id INTO sample_slot_id FROM slots 
    WHERE date = CURRENT_DATE + 2 AND start_time = '14:00:00' 
    LIMIT 1;
    
    IF sample_slot_id IS NOT NULL THEN
        UPDATE slots 
        SET is_available = FALSE, booking_id = bob_booking_id
        WHERE id = sample_slot_id;
    END IF;
    
    SELECT id INTO sample_slot_id FROM slots 
    WHERE date = CURRENT_DATE + 2 AND start_time = '14:30:00' 
    LIMIT 1;
    
    IF sample_slot_id IS NOT NULL THEN
        UPDATE slots 
        SET is_available = FALSE, booking_id = bob_booking_id
        WHERE id = sample_slot_id;
    END IF;
END $$;

-- Clean up the temporary function
DROP FUNCTION generate_sample_slots();
