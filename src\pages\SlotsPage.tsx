import { DatePicker } from "@/components/layout/datePicker";
import { But<PERSON> } from "@/components/ui/button";
import { slotsService, type Slots } from "@/lib/slotsService";
import {
  Calendar,
  CircleArrowRight,
  CircleX,
  Clock,
  PackageOpen,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const SlotsPage = () => {
  const [isDateSet, setIsDateSet] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string | undefined>(
    undefined
  );
  const [slotsFound, setSlotsFound] = useState<Slots[] | undefined>(undefined);
  const [selectedSlots, setSelectedSlots] = useState<Slots[] | undefined>(
    undefined
  );

  // handle date picking
  const handleDatePicking = (value: string | undefined) => {
    setSelectedDate(value);
    setIsDateSet(true);
  };

  // fetch slots for the selected date when it changes
  useEffect(() => {
    // const fetchSlots = async () => {
    //   if (selectedDate !== undefined) {
    //     const fetchedSlots = await slotsService.getSlots(selectedDate);
    //     setSlotsFound(fetchedSlots);
    //   }
    // };

    // fetchSlots();

    if(selectedDate !== undefined){
      slotsService.getSlots(selectedDate).then(response => {
      console.log('fetched slots: ', response);
      })
      
    }
    
  }, [selectedDate]);

  // handle slot selection (booking)
  const handleSlotSelection = (slot: Slots) => {
    // Check if selectedSlots is undefined or not
    if (selectedSlots === undefined) {
      setSelectedSlots([slot]);
      slot.isSelected = true;
    } else {
      // Check if slot already exists for that date
      const slotExists = selectedSlots.some(
        (existingSlot) => existingSlot.id === slot.id
      );

      if (!slotExists) {
        setSelectedSlots([...selectedSlots, slot]);
        slot.isSelected = true;
      } else {
        // Could add error handling or user notification here
        console.log("This slot is already selected");
      }
    }
  };

  // handle slot deselection (unbooking)
  const handleSlotDeselection = (slot: Slots) => {
    setSelectedSlots(selectedSlots?.filter((s) => s.id !== slot.id));
    slot.isSelected = false;
  };

  // handle checkout
  const navigate = useNavigate();
  const handleCheckout = () => {
    // navigate to checkout page
    // pass selected slots as state
    navigate("/checkout", { state: { selectedSlots: selectedSlots } });
  };

  return (
    <>
      <section>
        <div className="p-5">
          <h1 className="scroll-m-20 text-center text-[2rem] font-extrabold tracking-tight text-balance leading-[55px] text-gray-800">
            Available Slots
          </h1>
          <p className=" text-xl text-gray-700 text-center">
            Select your prefered date and time slot to book the field. all slots
            are 30 mins long
          </p>
        </div>
      </section>

      <section>
        <div>
          <div className="w-[90%] md:w-[90%] mx-auto border-[1px] border-gray-700 gap-5 rounded-lg p-5 md:p-10 bg-white flex flex-col md:flex-row md:items-center md:justify-between">
            <h2 className="flex items-center gap-2 font-bold text-gray-800 text-xl">
              <Calendar color="green" /> Select Date
            </h2>

            <div>
              <DatePicker sendDate={handleDatePicking} />
            </div>
          </div>
        </div>
      </section>

      {/* show slots when a date is picked */}
      {isDateSet && (
        <section className="mt-10">
          <div>
            <div className=" w-[90%] md:w-[90%] mx-auto p-5 bg-white">
              <p className="scroll-m-20 text-[1.3rem] tracking-tight text-balance text-gray-800">
                Available Slots for{" "}
                <span className="font-bold">{selectedDate}</span>{" "}
              </p>

              {/* slot cards */}
              <div className=" h-[90vh] overflow-y-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mt-10 h-full">
                  {slotsFound !== undefined && slotsFound?.length > 0 ? (
                    slotsFound?.map((slot) =>
                      slot.isAvailable ? (
                        <div
                          className={`border-[1px] border-gray-500 rounded-lg p-5 bg-white ${
                            slot.isSelected
                              ? "opacity-50 cursor-not-allowed"
                              : ""
                          }`}
                          key={slot.id}
                        >
                          <div className="flex items-center justify-between">
                            <p className="text-gray-800 font-bold flex items-center gap-1">
                              <Clock size={20} color="green" />
                              {slot.startTime} - {slot.endTime}
                            </p>
                            <span className="text-green-600 font-bold text-[.7rem] border-[1px] border-green-600 p-1 rounded-lg bg-green-100">
                              available
                            </span>
                          </div>
                          <p className="text-gray-700">Price: 4500Tsh</p>
                          <button
                            className={`w-full bg-green-600 text-white p-2 rounded-lg mt-5  ${
                              slot.isSelected
                                ? "cursor-not-allowed"
                                : "cursor-pointer"
                            }`}
                            disabled={slot.isSelected}
                            onClick={() => handleSlotSelection(slot)}
                          >
                            {slot.isSelected ? "Selected" : "Book"}
                          </button>
                        </div>
                      ) : (
                        <div
                          className="border-[1px] border-gray-500 rounded-lg p-5 bg-white opacity-50 cursor-not-allowed"
                          key={slot.id}
                        >
                          <div className="flex items-center justify-between">
                            <p className="text-gray-800 font-bold flex items-center gap-1">
                              <Clock size={20} color="green" />
                              {slot.startTime} - {slot.endTime}
                            </p>
                            <span className="text-red-600 font-bold text-[.7rem] border-[1px] border-red-600 p-1 rounded-lg ">
                              booked
                            </span>
                          </div>
                          <p className="text-gray-700">Price: 4500Tsh</p>
                          <button className="w-full bg-red-600 text-white p-2 rounded-lg mt-5 cursor-not-allowed">
                            Booked!
                          </button>
                        </div>
                      )
                    )
                  ) : (
                    <div>
                      <h1 className="scroll-m-20 text-left text-[1rem] md:text-[2rem] font-extrabold tracking-tight text-balance leading-[40px] md:leading-[55px] text-gray-600 flex items-center gap-2 opacity-70">
                        No time slots for this date
                        <PackageOpen size={150} />
                      </h1>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* show booked slots sections and price when a date is picked */}
      {isDateSet && (
        // booked slots section
        <section className="mt-10 pb-10">
          <div>
            <div className=" w-[90%] md:w-[90%] mx-auto p-5 bg-white">
              <p className="scroll-m-20 text-[1.3rem] tracking-tight text-balance text-gray-800">
                Selected Slots
              </p>

              {/* slot cards */}
              {selectedSlots !== undefined && selectedSlots.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mt-10">
                  {selectedSlots.map((slot) => (
                    <div
                      className="border-[1px] border-gray-500 rounded-lg p-1 md:p-3 bg-white"
                      key={slot.id}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-gray-800 font-bold flex items-center gap-1">
                            {slot.startTime} - {slot.endTime}
                          </p>
                          <p className="text-gray-600 font-semibold flex items-center gap-1">
                            {slot.date}
                          </p>
                        </div>
                        <Button
                          className="p-0 cursor-pointer bg-red-200 hover:bg-red-100"
                          color="red"
                          title="Delete Slot"
                          onClick={() => handleSlotDeselection(slot)}
                        >
                          <CircleX color="red" />{" "}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="mt-10 w-full">
                  <h1 className="scroll-m-20 text-center text-[1rem] md:text-[2rem] font-extrabold tracking-tight text-balance leading-[40px] md:leading-[55px] text-gray-600 flex items-center gap-2 opacity-70">
                    No slots selected
                    <PackageOpen size={60} />
                  </h1>
                </div>
              )}

              <div className="flex flex-col items-center gap-3 mt-10 md:mt-2">
                <h3 className="scroll-m-20 text-2xl font-bold tracking-tight text-center text-gray-700">
                  Total Amount
                </h3>
                <h1 className="scroll-m-20 text-center text-[2.3rem] md:text-[4rem] font-extrabold tracking-tight text-balance leading-[45px] md:leading-[55px] text-green-600">
                  {new Intl.NumberFormat("en-TZ", {
                    style: "currency",
                    currency: "TZS",
                  }).format(
                    selectedSlots?.reduce((acc, slot) => acc + slot.price, 0) ||
                      0
                  )}
                </h1>
                <Button
                  className="mt-5 p-4 md:p-7 text-lg md:text-xl flex items-center cursor-pointer"
                  onClick={handleCheckout}
                  disabled={
                    selectedSlots === undefined || selectedSlots.length === 0
                  }
                >
                  Proceed to payment <CircleArrowRight />{" "}
                </Button>
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default SlotsPage;
