-- FieldEase Database Schema - Table Creation
-- Execute this file first in Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT NOT NULL,
    date DATE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create slots table
CREATE TABLE IF NOT EXISTS slots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    price INTEGER NOT NULL, -- Price in cents (e.g., 15000 for $150.00)
    is_available BOOLEAN NOT NULL DEFAULT TRUE,
    booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create admin table
CREATE TABLE IF NOT EXISTS admin (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add constraints
ALTER TABLE bookings 
ADD CONSTRAINT bookings_name_check CHECK (length(trim(name)) > 0),
ADD CONSTRAINT bookings_phone_check CHECK (length(trim(phone)) > 0),
ADD CONSTRAINT bookings_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE slots
ADD CONSTRAINT slots_price_positive CHECK (price > 0),
ADD CONSTRAINT slots_time_order CHECK (start_time < end_time);

ALTER TABLE admin
ADD CONSTRAINT admin_name_check CHECK (length(trim(name)) > 0),
ADD CONSTRAINT admin_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Add comments for documentation
COMMENT ON TABLE bookings IS 'Customer bookings for field slots';
COMMENT ON TABLE slots IS 'Available time slots for field booking';
COMMENT ON TABLE admin IS 'Administrator accounts for managing the system';

COMMENT ON COLUMN bookings.name IS 'Customer full name';
COMMENT ON COLUMN bookings.phone IS 'Customer contact phone number';
COMMENT ON COLUMN bookings.email IS 'Customer email address';
COMMENT ON COLUMN bookings.date IS 'Date of the booking';

COMMENT ON COLUMN slots.date IS 'Date when the slot is available';
COMMENT ON COLUMN slots.start_time IS 'Slot start time';
COMMENT ON COLUMN slots.end_time IS 'Slot end time';
COMMENT ON COLUMN slots.price IS 'Price in cents/smallest currency unit';
COMMENT ON COLUMN slots.is_available IS 'FALSE when slot is booked, TRUE when available';
COMMENT ON COLUMN slots.booking_id IS 'References bookings.id, NULL when unbooked';

COMMENT ON COLUMN admin.name IS 'Administrator full name';
COMMENT ON COLUMN admin.email IS 'Administrator email address (unique)';
