import { CheckIcon, Download, Home, Info } from "lucide-react";
import qrSample from "../assets/qr_sample.png";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";

const Confirmation = () => {
  return (
    <>
      <section className="bg-gradient-to-r from-green-200 to-white p-5">
        <div className="p-5 flex flex-col items-center justify-center">
          <CheckIcon
            color="white"
            size={70}
            className="bg-green-600 p-5 rounded-full"
          />
          <h1 className="scroll-m-20 text-center text-[3rem] font-bold tracking-tight text-balance leading-[55px] text-gray-800 mt-10">
            Booking Confirmed
          </h1>

          {/* booking details */}
          <div className="bg-white p-5 rounded-lg mt-10 w-[90%] md:w-[50%] lg:w-[30%] mx-auto shadow-xl">
            <div className="flex items-center justify-between mx-auto mt-10">
              <p>Duration</p>
              <p className="font-bold">1 Hours and 30 Mins</p>
            </div>
            <div className="flex items-center justify-between mx-auto mt-10 ">
              <p>Amount</p>
              <p className="font-bold">45,000Tsh</p>
            </div>
            <div className="flex items-center justify-between mx-auto mt-10">
              <p>Date</p>
              <p className="font-bold">30 July, 2025</p>
            </div>

            <div className="flex items-center justify-between mx-auto mt-10 py-5 border-t-[1px] border-gray-300">
              <p>Booking ID</p>
              <p className="font-bold text-green-600 text-lg">A1B2C3</p>
            </div>
          </div>

          {/* qrcode section */}
          <div className=" w-[90%] md:w-[50%] lg:w-[30%] mx-auto flex flex-col items-center">
            <div className="p-5 border-[1px] border-yellow-600 bg-yellow-100 rounded-lg mt-10 flex items-center">
              <Info size={80} />
              <p className="text-center text-xl font-semibold text-gray-700 ml-2">
                Download the QR Code for future access. You wont be able to
                access it later
              </p>
            </div>
            <div className="mt-5 w-fit h-fit bg-white p-2 rounded-lg shadow-2xl">
              <img
                src={qrSample}
                alt="qr code"
                className="w-[300px] h-[300px]"
              />
            </div>
          </div>
        </div>

        <div className=" w-[90%] md:w-[50%] lg:w-[30%] mx-auto flex items-center justify-between mt-10">
          <Link
            to="/"
            className="flex items-center gap-2 text-white bg-green-600 p-3 rounded-lg w-fit cursor-pointer"
          >
            <Home /> Back to Home
          </Link>

          <Button className="p-6 cursor-pointer">
            <Download /> Download QR Code
          </Button>
        </div>
      </section>
    </>
  );
};

export default Confirmation;
