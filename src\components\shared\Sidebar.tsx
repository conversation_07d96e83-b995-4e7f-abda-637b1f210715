import { Home, Info, Phone } from "lucide-react";
import { Link } from "react-router-dom";

type sidebarProp = {
  isOpen: boolean;
  onClose: () => void;
};
const Sidebar = ({ isOpen, onClose }: sidebarProp) => {
  return (
    <>
      {/* Mobile sidebar overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div
            className="fixed inset-0 bg-gray-600 bg-opacity-75"
            onClick={onClose}
          />
        </div>
      )}

      {/* Sidebar */}
      <div
        className={`
      fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-dark-800 shadow-lg transform transition-all duration-300 ease-in-out md:translate-x-0 md:static md:hidden md:inset-0
      ${isOpen ? "translate-x-0" : "-translate-x-full"}
    `}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-dark-700 transition-theme duration-theme">
            <button
              onClick={onClose}
              className="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-400 dark:hover:bg-dark-700 transition-theme duration-theme"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-1">
            <Link
              to="/"
              onClick={onClose}
              className="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-theme duration-theme"
            >
              <span className="mr-3 flex-shrink-0 transition-theme duration-theme">
                <Home />
              </span>
              <span className="flex-1">Home</span>
            </Link>

            <Link
              to="/"
              onClick={onClose}
              className="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-theme duration-theme"
            >
              <span className="mr-3 flex-shrink-0 transition-theme duration-theme">
                <Info />
              </span>
              <span className="flex-1">About</span>
            </Link>

            <Link
              to="/"
              onClick={onClose}
              className="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-theme duration-theme"
            >
              <span className="mr-3 flex-shrink-0 transition-theme duration-theme">
                <Phone />
              </span>
              <span className="flex-1">Contac Us</span>
            </Link>
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
