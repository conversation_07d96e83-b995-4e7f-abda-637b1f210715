# FieldEase Slot Generation Scripts

This directory contains scripts to generate time slots for your FieldEase application in Supabase, matching exactly the logic from your frontend `generateTimeSlots()` function.

## 📁 Files Overview

### SQL Scripts (Execute in Supabase SQL Editor)

1. **`06_generate_slots_script.sql`** - Basic slot generation functions
2. **`07_advanced_slot_generator.sql`** - Advanced generation with business rules

### TypeScript Script (Run locally)

3. **`generateSlots.ts`** - Node.js script to generate slots via Supabase client

## 🚀 Quick Start

### Option 1: SQL Functions (Recommended)

Execute in Supabase SQL Editor:

```sql
-- Generate slots for next 60 days (skip Sundays)
SELECT * FROM generate_business_slots(
    CURRENT_DATE, 
    CURRENT_DATE + INTERVAL '60 days', 
    TRUE,  -- Skip Sundays
    FALSE, -- Don't skip Saturdays  
    15000  -- Price in cents
);
```

### Option 2: TypeScript Script

1. Install dependencies:
```bash
npm install @supabase/supabase-js tsx
```

2. Set environment variables:
```bash
# Add to your .env file
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
```

3. Run the script:
```bash
# Generate slots for next 90 days
npx tsx scripts/generateSlots.ts 90

# Generate slots for next 30 days and clear existing
npx tsx scripts/generateSlots.ts 30 --clear
```

## 📊 Slot Generation Logic

The scripts generate slots exactly like your frontend code:

- **Time Range**: 7:00 AM to 8:30 PM
- **Duration**: 30-minute slots
- **Price**: 15,000 cents (150.00 TZS)
- **Days**: Configurable (default skips Sundays)

### Generated Slots Per Day:
- 7:00-7:30 AM, 7:30-8:00 AM, 8:00-8:30 AM, ...
- ...continuing every 30 minutes...
- 7:30-8:00 PM, 8:00-8:30 PM (last slot)

**Total**: 27 slots per day × number of days

## 🛠️ Available SQL Functions

### Basic Functions

```sql
-- Generate slots for date range
SELECT generate_fieldease_slots('2024-08-01'::DATE, '2024-12-31'::DATE);

-- Generate with frontend compatibility
SELECT * FROM generate_slots_frontend_compatible(60); -- 60 days ahead
```

### Advanced Functions

```sql
-- Generate with business rules
SELECT * FROM generate_business_slots(
    start_date,     -- Start date
    end_date,       -- End date  
    skip_sundays,   -- Boolean: skip Sundays
    skip_saturdays, -- Boolean: skip Saturdays
    custom_price    -- Price in cents
);

-- Clear and regenerate slots
SELECT * FROM regenerate_slots(start_date, end_date);

-- Get slot statistics
SELECT * FROM get_slot_statistics();

-- Clear unbooked slots in range
SELECT clear_slots_in_range(start_date, end_date);
```

## 📈 Usage Examples

### Generate 3 months of slots (recommended)
```sql
SELECT * FROM generate_business_slots(
    CURRENT_DATE, 
    CURRENT_DATE + INTERVAL '90 days', 
    TRUE,  -- Skip Sundays (field closed)
    FALSE, -- Include Saturdays
    15000  -- 150.00 TZS per slot
);
```

### Weekend pricing (higher rates)
```sql
-- Generate weekday slots (regular price)
SELECT * FROM generate_business_slots(
    CURRENT_DATE, 
    CURRENT_DATE + INTERVAL '30 days', 
    TRUE, TRUE, 15000  -- Skip weekends
);

-- Generate weekend slots (premium price)  
SELECT * FROM generate_business_slots(
    CURRENT_DATE, 
    CURRENT_DATE + INTERVAL '30 days', 
    FALSE, FALSE, 25000  -- Include only weekends with higher price
);
```

### Check generation results
```sql
-- View today's slots
SELECT date, start_time, end_time, price, is_available
FROM slots 
WHERE date = CURRENT_DATE
ORDER BY start_time;

-- Get statistics
SELECT * FROM get_slot_statistics();
```

## 🔧 Integration with Your App

After generating slots, update your `slotsService.ts`:

```typescript
// Replace mock data with Supabase queries
export const slotsService = {
  async getSlots(date: string) {
    const formattedDate = new Date(date).toISOString().split('T')[0];
    const { data } = await supabase
      .from('slots')
      .select('*')
      .eq('date', formattedDate)
      .eq('is_available', true)
      .order('start_time');
    
    return data?.map(slot => ({
      id: slot.id,
      date: new Date(slot.date).toLocaleDateString("en-US", {
        month: "long", day: "2-digit", year: "numeric"
      }),
      startTime: formatTime(slot.start_time),
      endTime: formatTime(slot.end_time),
      price: slot.price,
      isAvailable: slot.is_available,
      isSelected: false
    })) || [];
  }
};
```

## ⚠️ Important Notes

- **Prices**: Stored in cents (15000 = 150.00 TZS)
- **Time Format**: 24-hour format in database, convert for display
- **Duplicates**: Scripts prevent duplicate slots with `ON CONFLICT DO NOTHING`
- **Booking Safety**: Only unbooked slots are cleared when regenerating
- **Performance**: Use batch operations for large date ranges

## 🎯 Recommended Workflow

1. **Initial Setup**: Generate 3 months of slots
2. **Weekly Maintenance**: Add new slots for upcoming weeks
3. **Monthly Cleanup**: Remove old unbooked slots
4. **Seasonal Updates**: Adjust pricing or hours as needed
