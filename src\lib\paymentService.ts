import axios from "axios";

const clientID = import.meta.env.VITE_AZAMPAY_CLIENT_ID;
const clientSecret = import.meta.env.VITE_AZAMPAY_CLIENT_SECRET;

const token_route =
  "https://authenticator-sandbox.azampay.co.tz/AppRegistration/GenerateToken";
const checkout_route = "https://sandbox.azampay.co.tz/azampay/mno/checkout";
const callback_route = "https://sandbox.azampay.co.tz/api/v1/Checkout/Callback";

export const paymentService = {
  // generate token for the app
  generateToken() {
    axios
      .post(token_route, {
        appName: "http://fieldease.netlify.app/",
        clientId: clientID,
        clientSecret: clientSecret,
      })
      .then((response) => {
        if (response.status === 200) {
          localStorage.setItem("token", response.data.data.accessToken);
          this.mnoCheckout();
        }
        console.log("response from azampay: ", response);
      })
      .catch((error) => {
        console.log("an error occurred: ", error);
      });
  },

  //   MNO checkout
  mnoCheckout() {
    const token = localStorage.getItem("token");
    if (token) {
      const externalID = `TID-${Date.now().toString()}`;
      axios
        .post(
          checkout_route,
          {
            accountNumber: "**********",
            amount: 10000,
            currency: "TZS",
            externalId: externalID,
            provider: "Airtel",
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        )
        .then((response) => {
          console.log("externalID generated: ", externalID);
          console.log("response from azampay checkout: ", response);
          if (response.status === 200) {
            this.callback(response.data.transactionId, "success");
          } else {
            this.callback(response.data.transactionId, "failure");
          }
        })
        .catch((error) => {
          console.log("an error occurred: ", error);
        });
    } else {
      console.log("token not found");
    }
  },

  //   callback function
  callback(transactionID: string, transactionStatus: string) {
    axios
      .post(callback_route, {
        msisdn: "**********",
        amount: "10000",
        message: "field booking fee",
        utilityref: clientID,
        operator: "Airtel",
        reference: transactionID,
        transactionstatus: transactionStatus,
      })
      .then((response) => {
        console.log("response from azampay callback: ", response);
      })
      .catch((error) => {
        console.log("an error occurred: ", error);
      });
  },
};
