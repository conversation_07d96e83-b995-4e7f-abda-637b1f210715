import { Link } from "react-router-dom";
import fieldImage from "../assets/field.jpg";
import { Clock, DollarSign, MapPinIcon } from "lucide-react";

const Home = () => {
  return (
    <div>
      <section className="md:h-[90vh] bg-gradient-to-r from-green-200 to-white p-5 md:p-10">
        <div className="p-5 py-10 md:py-20">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5 md:gap-3 w-[90%] md:w-[80%] mx-auto">
            <div className="flex flex-col gap-10">
              <h1 className="scroll-m-20 text-left text-[3rem] md:text-[4rem] font-extrabold tracking-tight text-balance leading-[45px] md:leading-[55px]">
                Book your field time online
              </h1>
              <p className=" text-xl md:text-2xl text-gray-800">
                Reserve your football field slot in seconds
              </p>
              <p className=" text-xl md:text-2xl bg-green-600 p-2 md:p-3 rounded-lg text-white w-fit">
                <Link to="/slots">View available slots</Link>
              </p>
            </div>
            <div>
              <img src={fieldImage} className="rounded-lg" />
            </div>
          </div>
        </div>
      </section>

      <section className="p-5">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-10 md:gap-2 w-[60%] p-5 mx-auto bg-gray-100 rounded-lg">
          <div className="flex flex-col items-center gap-2">
            <MapPinIcon size={30} color="green" />
            <h3 className="scroll-m-20 text-xl font-semibold tracking-tight text-center">
              Sports Field
            </h3>
            <p className="leading-7 text-[1.2rem] text-center text-gray-700">
              Msimbazi Sports Complex
            </p>
          </div>
          <div className="flex flex-col items-center gap-2">
            <Clock size={30} color="green" />
            <h3 className="scroll-m-20 text-xl font-semibold tracking-tight text-center">
              Flexible Hours
            </h3>
            <p className="leading-7 text-[1.2rem] text-center text-gray-700">
              7am - 10pm
            </p>
          </div>
          <div className="flex flex-col items-center gap-2">
            <DollarSign size={30} color="green" />
            <h3 className="scroll-m-20 text-xl font-semibold tracking-tight text-center">
              45,000Tsh / 1h & 30mins
            </h3>
            <p className="leading-7 text-[1.2rem] text-center text-gray-700">
              Competitive Pricing
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
